package services

import (
	"net/http"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
)

type IDashboardService interface {
	GetDashboardData() (*DashboardResponse, core.IError)
}

type DashboardResponse struct {
	ProjectStats      *ProjectStats      `json:"project_stats"`
	LatestProjects    []models.Project   `json:"latest_projects"`
	ProviderTypeStats []ProviderTypeStat `json:"provider_type_stats"`
}

type ProjectStats struct {
	Total  int64 `json:"total"`
	Draft  int64 `json:"draft"`
	Active int64 `json:"active"`
	Closed int64 `json:"closed"`
}

type ProviderTypeStat struct {
	ProviderType models.ProjectProviderType `json:"provider_type"`
	Count        int64                      `json:"count"`
	TotalUsage   float64                    `json:"total_usage"`
	TotalBudget  float64                    `json:"total_budget"`
	Percent      float64                    `json:"percent"`
}

type dashboardService struct {
	ctx core.IContext
}

func (s dashboardService) GetDashboardData() (*DashboardResponse, core.IError) {
	// Get project statistics by status
	projectStats, err := s.getProjectStats()
	if err != nil {
		return nil, err
	}

	// Get 5 latest projects
	latestProjects, err := s.getLatestProjects()
	if err != nil {
		return nil, err
	}

	// Get provider type statistics with usage
	providerStats, err := s.getProviderTypeStats()
	if err != nil {
		return nil, err
	}

	return &DashboardResponse{
		ProjectStats:      projectStats,
		LatestProjects:    latestProjects,
		ProviderTypeStats: providerStats,
	}, nil
}

func (s dashboardService) getProjectStats() (*ProjectStats, core.IError) {
	var stats ProjectStats

	// Get total count
	count, err := repo.Project(s.ctx).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get total project count",
		}, err)
	}
	stats.Total = count

	// Get count by status
	draftCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusDraft).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get draft project count",
		}, err)
	}
	stats.Draft = draftCount

	activeCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusActive).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get active project count",
		}, err)
	}
	stats.Active = activeCount

	closedCount, err := repo.Project(s.ctx).Where("status = ?", models.ProjectStatusClosed).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get closed project count",
		}, err)
	}
	stats.Closed = closedCount

	return &stats, nil
}

func (s dashboardService) getLatestProjects() ([]models.Project, core.IError) {
	var projects []models.Project

	projects, err := repo.Project(s.ctx, repo.ProjectWithAllRelations(), repo.ProjectOrderBy(&core.PageOptions{})).
		Limit(5).
		FindAll()

	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FIND_FAILED",
			Message: "Failed to get latest projects",
		}, err)
	}

	return projects, nil
}

func (s dashboardService) getProviderTypeStats() ([]ProviderTypeStat, core.IError) {
	var stats []ProviderTypeStat

	// Get all provider types
	providerTypes := []models.ProjectProviderType{
		models.ProjectProviderTypeHWC,
		models.ProjectProviderTypeAWS,
		models.ProjectProviderTypeCHM,
	}

	for _, providerType := range providerTypes {
		var totalUsage float64
		var totalBudget float64

		// Get project count by provider type (Active status only)
		count, err := repo.Project(s.ctx).Where("provider_type = ? AND status = ?", providerType, models.ProjectStatusActive).Count()
		if err != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "COUNT_FAILED",
				Message: "Failed to get provider type count",
			}, err)
		}

		// Get total usage for this provider type
		// subquery หา project_usages ล่าสุดของแต่ละ project
		latestUsageSub := s.ctx.DB().
			Table("project_usages pu1").
			Select("pu1.project_id, pu1.amount").
			Where("pu1.created_at = (SELECT MAX(pu2.created_at) FROM project_usages pu2 WHERE pu2.project_id = pu1.project_id)")
		// Join with project_usages to get the sum of amounts (Active status only)
		dbErr := s.ctx.DB().Table("projects").
			Select("COALESCE(SUM(latest.amount), 0) as total_usage").
			Joins("LEFT JOIN (?) AS latest ON projects.id = latest.project_id", latestUsageSub).
			Where("projects.provider_type = ? AND projects.status = ? AND projects.deleted_at IS NULL", providerType, models.ProjectStatusActive).
			Scan(&totalUsage).Error

		if dbErr != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "DB_QUERY_FAILED",
				Message: "Failed to get provider type usage",
			}, core.Error{})
		}

		// Get total budget for this provider type (Active status only)
		dbErr = s.ctx.DB().Table("projects").
			Select("COALESCE(SUM(budget), 0) as total_budget").
			Where("provider_type = ? AND status = ? AND deleted_at IS NULL", providerType, models.ProjectStatusActive).
			Scan(&totalBudget).Error

		if dbErr != nil {
			return nil, s.ctx.NewError(core.Error{
				Status:  http.StatusInternalServerError,
				Code:    "DB_QUERY_FAILED",
				Message: "Failed to get provider type budget",
			}, core.Error{})
		}

		// Calculate percentage (usage / budget * 100)
		var percent float64
		if totalBudget > 0 {
			percent = (totalUsage / totalBudget) * 100
		}

		stats = append(stats, ProviderTypeStat{
			ProviderType: providerType,
			Count:        count,
			TotalUsage:   totalUsage,
			TotalBudget:  totalBudget,
			Percent:      percent,
		})
	}

	return stats, nil
}

func NewDashboardService(ctx core.IContext) IDashboardService {
	return &dashboardService{ctx: ctx}
}
